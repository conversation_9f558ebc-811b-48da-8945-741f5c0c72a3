import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface DealWithInfo {
  id: string;
  title: string;
  description: string | null;
  original_price: number;
  discounted_price: number;
  discount_percentage: number | null;
  start_date: string;
  end_date: string;
  status: 'draft' | 'published' | 'expired';
  business_name: string;
  business_address: string | null;
  category_name: string | null;
}

interface AdminDealEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  deal: DealWithInfo | null;
  onSuccess: () => void;
}

export const AdminDealEditModal = ({ isOpen, onClose, deal, onSuccess }: AdminDealEditModalProps) => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    original_price: "",
    discounted_price: "",
    discount_percentage: "",
    start_date: "",
    end_date: "",
    status: "draft" as 'draft' | 'published' | 'expired'
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (deal) {
      setFormData({
        title: deal.title || "",
        description: deal.description || "",
        original_price: deal.original_price?.toString() || "",
        discounted_price: deal.discounted_price?.toString() || "",
        discount_percentage: deal.discount_percentage?.toString() || "",
        start_date: deal.start_date ? new Date(deal.start_date).toISOString().split('T')[0] : "",
        end_date: deal.end_date ? new Date(deal.end_date).toISOString().split('T')[0] : "",
        status: deal.status || "draft"
      });
    } else {
      // Reset form when no deal is selected
      setFormData({
        title: "",
        description: "",
        original_price: "",
        discounted_price: "",
        discount_percentage: "",
        start_date: "",
        end_date: "",
        status: "draft"
      });
    }
  }, [deal]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!deal) return;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from("deals")
        .update({
          title: formData.title,
          description: formData.description,
          original_price: parseFloat(formData.original_price) || 0,
          discounted_price: parseFloat(formData.discounted_price) || 0,
          discount_percentage: parseFloat(formData.discount_percentage) || 0,
          start_date: formData.start_date,
          end_date: formData.end_date,
          status: formData.status,
        })
        .eq("id", deal.id);

      if (error) throw error;

      toast.success("Offerta aggiornata con successo");
      onSuccess();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Errore durante l'aggiornamento dell'offerta");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-calculate discounted price when original price or discount percentage changes
    if (field === 'original_price' || field === 'discount_percentage') {
      const originalPrice = field === 'original_price' ? parseFloat(value) : parseFloat(formData.original_price);
      const discountPercentage = field === 'discount_percentage' ? parseFloat(value) : parseFloat(formData.discount_percentage);
      
      if (originalPrice && discountPercentage) {
        const discountedPrice = originalPrice * (1 - discountPercentage / 100);
        setFormData(prev => ({ ...prev, discounted_price: discountedPrice.toFixed(2) }));
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Modifica Offerta</DialogTitle>
          <DialogDescription>
            Modifica i dettagli dell'offerta. I campi contrassegnati con * sono obbligatori.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Titolo *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Titolo dell'offerta"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrizione</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Descrizione dell'offerta"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="original_price">Prezzo Originale (€) *</Label>
              <Input
                id="original_price"
                type="number"
                step="0.01"
                min="0"
                value={formData.original_price}
                onChange={(e) => handleInputChange('original_price', e.target.value)}
                placeholder="0.00"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="discount_percentage">Sconto (%) *</Label>
              <Input
                id="discount_percentage"
                type="number"
                step="0.01"
                min="0"
                max="100"
                value={formData.discount_percentage}
                onChange={(e) => handleInputChange('discount_percentage', e.target.value)}
                placeholder="0"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="discounted_price">Prezzo Scontato (€)</Label>
            <Input
              id="discounted_price"
              type="number"
              step="0.01"
              min="0"
              value={formData.discounted_price}
              onChange={(e) => handleInputChange('discounted_price', e.target.value)}
              placeholder="0.00"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_date">Data Inizio *</Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) => handleInputChange('start_date', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end_date">Data Fine *</Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => handleInputChange('end_date', e.target.value)}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Stato</Label>
            <Select value={formData.status} onValueChange={(value: 'draft' | 'published' | 'expired') => handleInputChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona stato" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Bozza</SelectItem>
                <SelectItem value="published">Pubblicata</SelectItem>
                <SelectItem value="expired">Scaduta</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
              Annulla
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Salvando..." : "Salva Modifiche"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
