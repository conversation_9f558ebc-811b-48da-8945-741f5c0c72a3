import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { X } from "lucide-react";

interface DealCategory {
  id: string;
  name: string;
  category_id: string;
}

interface DealWithInfo {
  id: string;
  title: string;
  description: string | null;
  original_price: number;
  discounted_price: number;
  discount_percentage: number | null;
  start_date: string;
  end_date: string;
  status: 'draft' | 'published' | 'expired';
  business_name: string;
  business_address: string | null;
  category_name: string | null;
  deal_categories?: DealCategory[];
}

interface AdminDealEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  deal: DealWithInfo | null;
  onSuccess: () => void;
}

export const AdminDealEditModal = ({ isOpen, onClose, deal, onSuccess }: AdminDealEditModalProps) => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    original_price: "",
    discounted_price: "",
    discount_percentage: "",
    start_date: "",
    end_date: "",
    status: "draft" as 'draft' | 'published' | 'expired'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [availableDealCategories, setAvailableDealCategories] = useState<DealCategory[]>([]);
  const [selectedDealCategories, setSelectedDealCategories] = useState<string[]>([]);

  // Fetch available deal categories
  const fetchDealCategories = async () => {
    try {
      const { data, error } = await supabase
        .from("deal_categories")
        .select("id, name, category_id")
        .order("name");

      if (error) throw error;
      setAvailableDealCategories(data || []);
    } catch (error) {
      console.error("Error fetching deal categories:", error);
    }
  };

  // Fetch current deal categories for the deal
  const fetchCurrentDealCategories = async (dealId: string) => {
    try {
      const { data, error } = await supabase
        .from("deals_deal_categories")
        .select("deal_category_id")
        .eq("deal_id", dealId);

      if (error) throw error;
      setSelectedDealCategories(data?.map(item => item.deal_category_id) || []);
    } catch (error) {
      console.error("Error fetching current deal categories:", error);
    }
  };

  useEffect(() => {
    fetchDealCategories();
  }, []);

  useEffect(() => {
    if (deal) {
      setFormData({
        title: deal.title || "",
        description: deal.description || "",
        original_price: deal.original_price?.toString() || "",
        discounted_price: deal.discounted_price?.toString() || "",
        discount_percentage: deal.discount_percentage?.toString() || "",
        start_date: deal.start_date ? new Date(deal.start_date).toISOString().split('T')[0] : "",
        end_date: deal.end_date ? new Date(deal.end_date).toISOString().split('T')[0] : "",
        status: deal.status || "draft"
      });
      fetchCurrentDealCategories(deal.id);
    } else {
      // Reset form when no deal is selected
      setFormData({
        title: "",
        description: "",
        original_price: "",
        discounted_price: "",
        discount_percentage: "",
        start_date: "",
        end_date: "",
        status: "draft"
      });
      setSelectedDealCategories([]);
    }
  }, [deal]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!deal) return;

    setIsLoading(true);
    try {
      // Update deal basic information
      const { error: dealError } = await supabase
        .from("deals")
        .update({
          title: formData.title,
          description: formData.description,
          original_price: parseFloat(formData.original_price) || 0,
          discounted_price: parseFloat(formData.discounted_price) || 0,
          discount_percentage: parseFloat(formData.discount_percentage) || 0,
          start_date: formData.start_date,
          end_date: formData.end_date,
          status: formData.status,
        })
        .eq("id", deal.id);

      if (dealError) throw dealError;

      // Update deal categories
      // First, delete existing associations
      const { error: deleteError } = await supabase
        .from("deals_deal_categories")
        .delete()
        .eq("deal_id", deal.id);

      if (deleteError) throw deleteError;

      // Then, insert new associations
      if (selectedDealCategories.length > 0) {
        const associations = selectedDealCategories.map(categoryId => ({
          deal_id: deal.id,
          deal_category_id: categoryId
        }));

        const { error: insertError } = await supabase
          .from("deals_deal_categories")
          .insert(associations);

        if (insertError) throw insertError;
      }

      toast.success("Offerta aggiornata con successo");
      onSuccess();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Errore durante l'aggiornamento dell'offerta");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-calculate discounted price when original price or discount percentage changes
    if (field === 'original_price' || field === 'discount_percentage') {
      const originalPrice = field === 'original_price' ? parseFloat(value) : parseFloat(formData.original_price);
      const discountPercentage = field === 'discount_percentage' ? parseFloat(value) : parseFloat(formData.discount_percentage);
      
      if (originalPrice && discountPercentage) {
        const discountedPrice = originalPrice * (1 - discountPercentage / 100);
        setFormData(prev => ({ ...prev, discounted_price: discountedPrice.toFixed(2) }));
      }
    }
  };

  const handleDealCategoryToggle = (categoryId: string) => {
    setSelectedDealCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const removeDealCategory = (categoryId: string) => {
    setSelectedDealCategories(prev => prev.filter(id => id !== categoryId));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Modifica Offerta</DialogTitle>
          <DialogDescription>
            Modifica i dettagli dell'offerta. I campi contrassegnati con * sono obbligatori.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Titolo *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Titolo dell'offerta"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrizione</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Descrizione dell'offerta"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="original_price">Prezzo Originale (€) *</Label>
              <Input
                id="original_price"
                type="number"
                step="0.01"
                min="0"
                value={formData.original_price}
                onChange={(e) => handleInputChange('original_price', e.target.value)}
                placeholder="0.00"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="discount_percentage">Sconto (%) *</Label>
              <Input
                id="discount_percentage"
                type="number"
                step="0.01"
                min="0"
                max="100"
                value={formData.discount_percentage}
                onChange={(e) => handleInputChange('discount_percentage', e.target.value)}
                placeholder="0"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="discounted_price">Prezzo Scontato (€)</Label>
            <Input
              id="discounted_price"
              type="number"
              step="0.01"
              min="0"
              value={formData.discounted_price}
              onChange={(e) => handleInputChange('discounted_price', e.target.value)}
              placeholder="0.00"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_date">Data Inizio *</Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) => handleInputChange('start_date', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end_date">Data Fine *</Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => handleInputChange('end_date', e.target.value)}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Stato</Label>
            <Select value={formData.status} onValueChange={(value: 'draft' | 'published' | 'expired') => handleInputChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona stato" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Bozza</SelectItem>
                <SelectItem value="published">Pubblicata</SelectItem>
                <SelectItem value="expired">Scaduta</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <Label>Categorie Deal</Label>

            {/* Selected categories display */}
            {selectedDealCategories.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {selectedDealCategories.map(categoryId => {
                  const category = availableDealCategories.find(c => c.id === categoryId);
                  return category ? (
                    <Badge key={categoryId} variant="secondary" className="flex items-center gap-1">
                      {category.name}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => removeDealCategory(categoryId)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ) : null;
                })}
              </div>
            )}

            {/* Available categories selection */}
            <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
              <div className="text-sm text-muted-foreground mb-2">
                Seleziona le categorie per questa offerta:
              </div>
              <div className="space-y-2">
                {availableDealCategories.map(category => (
                  <div key={category.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={selectedDealCategories.includes(category.id)}
                      onCheckedChange={() => handleDealCategoryToggle(category.id)}
                    />
                    <Label
                      htmlFor={`category-${category.id}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {category.name}
                    </Label>
                  </div>
                ))}
                {availableDealCategories.length === 0 && (
                  <div className="text-sm text-muted-foreground">
                    Nessuna categoria deal disponibile
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
              Annulla
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Salvando..." : "Salva Modifiche"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
