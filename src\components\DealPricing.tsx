import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";

interface DealPricingProps {
  originalPrice: string;
  discountPercentage: string;
  discountedPrice: string;
  onOriginalPriceChange: (value: string) => void;
  onDiscountPercentageChange: (value: string) => void;
}

const DealPricing = ({
  originalPrice,
  discountPercentage,
  discountedPrice,
  onOriginalPriceChange,
  onDiscountPercentageChange,
}: DealPricingProps) => {
  const [priceOrDiscount, setPriceOrDiscount] = useState<"Price" | "Discount">(
    "Price"
  );
  const [isDirectDiscount, setIsDirectDiscount] = useState(false);
  const [localDiscountedPrice, setLocalDiscountedPrice] = useState(discountedPrice);

  // Update local state when props change
  useEffect(() => {
    setLocalDiscountedPrice(discountedPrice);
  }, [discountedPrice]);

  // Set mode based on original price
  useEffect(() => {
    const original = parseFloat(originalPrice);
    if (original === 0) {
      setPriceOrDiscount("Discount");
      setIsDirectDiscount(true);
    } else if (!isNaN(original) && original > 0) {
      setPriceOrDiscount("Price");
      setIsDirectDiscount(false);
    }
  }, [originalPrice]);

  // Calculate the discounted price based on original price and discount percentage
  const calculateDiscountedPrice = () => {
    if (originalPrice && discountPercentage) {
      const original = parseFloat(originalPrice);
      const discount = parseFloat(discountPercentage);
      if (!isNaN(original) && !isNaN(discount) && original > 0) {
        return (original - (original * discount) / 100).toFixed(2);
      }
    }
    return "0.00";
  };

  // Handle direct discount checkbox change
  const handleDirectDiscountChange = (checked: boolean) => {
    setIsDirectDiscount(checked);
    if (checked) {
      onOriginalPriceChange("0");
    } else if (originalPrice === "0") {
      onOriginalPriceChange("1");
    }
  };

  // Handle price mode discounted price change
  const handleDiscountedPriceChange = (value: string) => {
    setLocalDiscountedPrice(value);

    if (originalPrice && value) {
      const original = parseFloat(originalPrice);
      const discounted = parseFloat(value);

      if (!isNaN(original) && !isNaN(discounted) && original > 0) {
        const calculatedDiscount = (((original - discounted) / original) * 100).toFixed(2);
        onDiscountPercentageChange(calculatedDiscount);
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Toggle Group */}
      <ToggleGroup
        className="grid grid-cols-2 w-full"
        variant="outline"
        type="single"
        value={priceOrDiscount}
        onValueChange={(value) =>
          value && setPriceOrDiscount(value as "Price" | "Discount")
        }
      >
        <ToggleGroupItem
          value="Discount"
          aria-label="Toggle discount"
          className="data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
        >
          Sconto
        </ToggleGroupItem>
        <ToggleGroupItem
          value="Price"
          aria-label="Toggle price"
          className="data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
        >
          Importo
        </ToggleGroupItem>
      </ToggleGroup>

      {priceOrDiscount === "Discount" ? (
        <div className="space-y-6">
          {/* Checkbox for "Sconto Secco" */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="sconto-secco"
              checked={isDirectDiscount}
              onCheckedChange={(checked) => handleDirectDiscountChange(checked as boolean)}
            />
            <Label htmlFor="sconto-secco" className="cursor-pointer">
              Sconto Secco
            </Label>
          </div>

          {/* Input fields grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {!isDirectDiscount && (
              <div>
                <Label className="text-sm font-medium">Prezzo Originale</Label>
                <Input
                  type="number"
                  step="0.01"
                  required
                  value={originalPrice}
                  onChange={(e) => onOriginalPriceChange(e.target.value)}
                  placeholder="€0.00"
                />
              </div>
            )}
            <div>
              <Label className="text-sm font-medium">Sconto %</Label>
              <Input
                type="number"
                step="0.01"
                required
                value={discountPercentage}
                onChange={(e) => onDiscountPercentageChange(e.target.value)}
                placeholder="0%"
              />
            </div>
            {!isDirectDiscount && (
              <div>
                <Label className="text-sm font-medium">Prezzo Scontato</Label>
                <Input
                  type="number"
                  step="0.01"
                  readOnly
                  value={calculateDiscountedPrice()}
                  className="bg-muted"
                  placeholder="€0.00"
                />
              </div>
            )}
          </div>

          {/* Result display */}
          <div className="p-4 bg-muted rounded-lg">
            <Label className="text-sm font-semibold">
              {isDirectDiscount ? "Sconto Applicato" : "Importo Scontato"}
            </Label>
            <div className="text-2xl font-bold text-primary">
              {isDirectDiscount
                ? `${discountPercentage}%`
                : (
                  <>
                    €{calculateDiscountedPrice()}
                    <span className="text-sm font-medium text-muted-foreground ml-2">
                      ({discountPercentage}% di sconto)
                    </span>
                  </>
                )}
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Prezzo Originale</Label>
              <Input
                type="number"
                step="0.01"
                required
                value={originalPrice}
                onChange={(e) => onOriginalPriceChange(e.target.value)}
                placeholder="€0.00"
              />
            </div>
            <div>
              <Label className="text-sm font-medium">Prezzo Scontato</Label>
              <Input
                type="number"
                step="0.01"
                required
                value={localDiscountedPrice}
                onChange={(e) => handleDiscountedPriceChange(e.target.value)}
                placeholder="€0.00"
              />
            </div>
          </div>

          {/* Discount Percentage Display */}
          <div className="p-4 bg-muted rounded-lg">
            <Label className="text-sm font-semibold">Sconto Applicato</Label>
            <div className="text-2xl font-bold text-primary">
              {discountPercentage || "0"}%
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DealPricing;