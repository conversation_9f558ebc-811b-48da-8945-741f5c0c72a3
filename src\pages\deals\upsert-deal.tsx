import { type FormData } from "@/types/deals";
import { toast } from "sonner";
import { Sparkles, ImageIcon, ChevronLeft, ChevronRight, Check } from "lucide-react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useBusinessStore } from "@/store/businessStore";
import MainLayout from "@/layouts/MainLayout";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import TimeSlotManager from "@/components/TimeSlotManager";
import DealPricing from "@/components/DealPricing";
import DealImages from "@/components/DealImages";

const targetAudiences = [
  { value: "famiglie", label: "Famiglie" },
  { value: "giovani", label: "Giovani" },
  { value: "professionisti", label: "Professionisti" },
  { value: "anziani", label: "Anziani" },
];

// Steps for the form
const steps = ["Titolo", "Immagini", "Prezzi", "Orari", "Riepilogo"];

const UpsertDeal = () => {
  const navigate = useNavigate();
  const selectedBusiness = useBusinessStore(state => state.selectedBusiness);
  
  const [formData, setFormData] = useState<FormData>({
    title: "",
    description: "",
    original_price: "",
    discount_percentage: "",
    discounted_price: "",
    start_date: "",
    end_date: "",
    time_slots: {
      schedule: [
        { day: 1, day_name: "Lunedì", time_slots: [] },
        { day: 2, day_name: "Martedì", time_slots: [] },
        { day: 3, day_name: "Mercoledì", time_slots: [] },
        { day: 4, day_name: "Giovedì", time_slots: [] },
        { day: 5, day_name: "Venerdì", time_slots: [] },
        { day: 6, day_name: "Sabato", time_slots: [] },
        { day: 7, day_name: "Domenica", time_slots: [] },
      ],
      exceptions: [],
    },
    images: [],
    status: "draft",
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedTargetAudience, setSelectedTargetAudience] = useState(targetAudiences[0].value);
  const [businessType, setBusinessType] = useState<string>("");
  const [businessName, setBusinessName] = useState<string>("");
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [imagePrompt, setImagePrompt] = useState("");
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [generatedImageUrl, setGeneratedImageUrl] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const fetchBusinessInfo = async () => {
      if (!selectedBusiness) return;
      
      try {
        const { data: business, error: businessError } = await supabase
          .from("businesses")
          .select("category_id, name")
          .eq("id", selectedBusiness.id)
          .maybeSingle();

        if (businessError) throw businessError;

        if (business?.name) {
          setBusinessName(business.name);
        }

        if (business?.category_id) {
          const { data: category, error: categoryError } = await supabase
            .from("categories")
            .select("name")
            .eq("id", business.category_id)
            .maybeSingle();

          if (categoryError) throw categoryError;

          if (category?.name) {
            setBusinessType(category.name.toLowerCase());
          }
        }
      } catch (error) {
        console.error("Errore nel recupero delle informazioni business:", error);
        toast.error("Errore nel recupero delle informazioni business");
      }
    };

    fetchBusinessInfo();
  }, [selectedBusiness]);

  const generateContent = async () => {
    if (!businessType) {
      toast.error("Categoria business non disponibile");
      return;
    }

    try {
      setIsGenerating(true);
      const { data, error } = await supabase.functions.invoke("generate-deal-content", {
        body: {
          businessType,
          targetAudience: selectedTargetAudience,
        },
      });

      if (error) throw error;

      if (data.title) {
        setFormData((prev) => ({ ...prev, title: data.title }));
      }
      if (data.description) {
        setFormData((prev) => ({ ...prev, description: data.description }));
      }
    } catch (error) {
      console.error("Errore durante la generazione del contenuto:", error);
      toast.error("Errore durante la generazione del contenuto");
    } finally {
      setIsGenerating(false);
    }
  };

  const generateImage = async () => {
    if (!businessType && !imagePrompt) {
      toast.error("Inserisci una descrizione dell'immagine");
      return;
    }

    try {
      setIsGeneratingImage(true);
      setGeneratedImageUrl(null);

      const { data, error } = await supabase.functions.invoke("generate-business-image", {
        body: {
          prompt: imagePrompt,
          businessType,
          businessName,
        },
      });

      if (error) {
        console.error("Errore chiamata edge function:", error);
        throw error;
      }

      if (!data.success) {
        throw new Error(data.message || "Errore durante la generazione dell'immagine");
      }

      if (data.imageUrl) {
        setGeneratedImageUrl(data.imageUrl);
      } else if (data.id) {
        toast.info("La generazione dell'immagine potrebbe richiedere qualche minuto");
      }
    } catch (error) {
      console.error("Errore completo:", error);
      toast.error("Errore durante la generazione dell'immagine");
    } finally {
      setIsGeneratingImage(false);
    }
  };

  const saveGeneratedImage = async () => {
    if (!generatedImageUrl) return;

    try {
      const response = await fetch(generatedImageUrl);
      if (!response.ok) throw new Error("Impossibile scaricare l'immagine");

      const blob = await response.blob();
      const file = new File([blob], `immagine-generata-${Date.now()}.jpg`, {
        type: "image/jpeg",
      });

      setFormData((prev) => ({
        ...prev,
        images: [...prev.images, file],
      }));

      setIsImageDialogOpen(false);
      setGeneratedImageUrl(null);
      setImagePrompt("");
    } catch (error) {
      console.error("Errore durante il salvataggio dell'immagine:", error);
      toast.error("Errore durante il salvataggio dell'immagine");
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 0: // Title & Description
        return formData.title.trim() !== "" && formData.description.trim() !== "";
      case 1: // Images
        return formData.images.length > 0;
      case 2: // Pricing
        return formData.original_price !== "" && formData.discount_percentage !== "";
      case 3: // Timing
        return formData.start_date !== "" && formData.end_date !== "";
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (isStepValid()) {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
    } else {
      toast.error("Completa tutti i campi obbligatori prima di procedere");
    }
  };

  const handlePrev = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const uploadImages = async (dealId: string): Promise<string[]> => {
    if (formData.images.length === 0) return [];

    const uploadedUrls: string[] = [];

    try {
      for (const image of formData.images) {
        const fileExt = image.name.split('.').pop();
        const filePath = `deals/${dealId}/${crypto.randomUUID()}.${fileExt}`;

        const { error: uploadError } = await supabase.storage
          .from('business-photos')
          .upload(filePath, image, { upsert: true });

        if (uploadError) throw uploadError;

        const { data: { publicUrl } } = supabase.storage
          .from('business-photos')
          .getPublicUrl(filePath);

        uploadedUrls.push(publicUrl);
      }

      return uploadedUrls;
    } catch (error) {
      console.error('Errore upload immagini:', error);
      toast.error("Errore durante il caricamento delle immagini");
      return [];
    }
  };

  const handleSubmit = async () => {
    if (!selectedBusiness) {
      toast.error("Nessuna attività selezionata");
      return;
    }

    try {
      setIsSubmitting(true);
      
      const { data: dealData, error } = await supabase
        .from("deals")
        .insert({
          title: formData.title,
          description: formData.description,
          original_price: parseFloat(formData.original_price) || 0,
          discounted_price: parseFloat(formData.discounted_price) || 0,
          discount_percentage: parseFloat(formData.discount_percentage) || 0,
          start_date: formData.start_date,
          end_date: formData.end_date,
          business_id: selectedBusiness.id,
          status: formData.status,
          auto_confirm: true,
          terms_conditions: "Informazioni importanti - La prenotazione non è rimborsabile - Presentati 5 minuti prima dell'orario prenotato - Porta con te un documento d'identità",
          time_slots: formData.time_slots,
        })
        .select()
        .single();

      if (error) throw error;

      // Upload images if any
      const imageUrls = await uploadImages(dealData.id);
      if (imageUrls.length > 0) {
        const { error: updateError } = await supabase
          .from("deals")
          .update({ images: imageUrls })
          .eq("id", dealData.id);
        
        if (updateError) throw updateError;
      }

      toast.success("Offerta creata con successo");
      navigate("/deals");
    } catch (error) {
      console.error("Errore creazione offerta:", error);
      toast.error("Errore nella creazione dell'offerta");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate("/deals");
  };

  // Stepper component
  const Stepper = () => {
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between max-w-2xl mx-auto">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center border-2 transition-colors ${
                  index < currentStep
                    ? "bg-primary text-primary-foreground border-primary"
                    : index === currentStep
                    ? "border-primary text-primary bg-background"
                    : "border-muted-foreground/25 text-muted-foreground"
                }`}
              >
                {index < currentStep ? (
                  <Check className="h-5 w-5" />
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>
              <span
                className={`text-sm mt-2 text-center ${
                  index === currentStep
                    ? "text-primary font-medium"
                    : "text-muted-foreground"
                }`}
              >
                {step}
              </span>
            </div>
          ))}
        </div>
        <div className="relative mt-4 max-w-2xl mx-auto">
          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-full h-0.5 bg-muted"></div>
          <div
            className="absolute left-0 top-1/2 transform -translate-y-1/2 h-0.5 bg-primary transition-all duration-300"
            style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
          ></div>
        </div>
      </div>
    );
  };

  // Form steps content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Title & Description
        return (
          <div className="max-w-2xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Informazioni Base</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <label className="block text-sm font-medium">
                      Titolo dell'offerta
                    </label>
                    <div className="flex items-center gap-4">
                      <Select
                        value={selectedTargetAudience}
                        onValueChange={setSelectedTargetAudience}
                      >
                        <SelectTrigger className="w-[140px]">
                          <SelectValue placeholder="Seleziona target" />
                        </SelectTrigger>
                        <SelectContent>
                          {targetAudiences.map((audience) => (
                            <SelectItem key={audience.value} value={audience.value}>
                              {audience.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        type="button"
                        onClick={generateContent}
                        disabled={isGenerating || !businessType}
                        variant="outline"
                        size="sm"
                      >
                        <Sparkles className="h-4 w-4 mr-2" />
                        {isGenerating ? "Generazione..." : "Genera contenuto"}
                      </Button>
                    </div>
                  </div>
                  <Input
                    type="text"
                    required
                    value={formData.title}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, title: e.target.value }))
                    }
                    placeholder="Inserisci il titolo dell'offerta"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Descrizione
                  </label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    rows={4}
                    placeholder="Descrivi la tua offerta"
                    required
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 1: // Images
        return (
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Immagini</CardTitle>
                  <Button
                    type="button"
                    onClick={() => setIsImageDialogOpen(true)}
                    variant="outline"
                    size="sm"
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    Genera immagine
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <DealImages
                  images={formData.images}
                  onImagesChange={(e) => {
                    const files = Array.from(e.target.files || []);
                    setFormData((prev) => ({
                      ...prev,
                      images: [...prev.images, ...files],
                    }));
                  }}
                  onImageRemove={(index) => {
                    setFormData((prev) => ({
                      ...prev,
                      images: prev.images.filter((_, i) => i !== index),
                    }));
                  }}
                />
              </CardContent>
            </Card>
          </div>
        );

      case 2: // Pricing
        return (
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>Prezzi</CardTitle>
              </CardHeader>
              <CardContent>
                <DealPricing
                  originalPrice={formData.original_price}
                  discountPercentage={formData.discount_percentage}
                  discountedPrice={formData.discounted_price}
                  onOriginalPriceChange={(value) =>
                    setFormData((prev) => ({ ...prev, original_price: value }))
                  }
                  onDiscountPercentageChange={(value) =>
                    setFormData((prev) => ({ ...prev, discount_percentage: value }))
                  }
                />
              </CardContent>
            </Card>
          </div>
        );

      case 3: // Timing
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Date di validità</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Data inizio
                    </label>
                    <Input
                      type="date"
                      required
                      value={formData.start_date}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          start_date: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Data fine
                    </label>
                    <Input
                      type="date"
                      required
                      value={formData.end_date}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          end_date: e.target.value,
                        }))
                      }
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
            <TimeSlotManager
              value={formData.time_slots}
              onChange={(newSchedule) =>
                setFormData((prev) => ({ ...prev, time_slots: newSchedule }))
              }
            />
          </div>
        );

      case 4: // Summary
        return (
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>Riepilogo</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium text-lg">{formData.title}</h3>
                    <p className="text-muted-foreground">{formData.description}</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Prezzo originale</p>
                      <p className="font-medium">€{formData.original_price}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Sconto</p>
                      <p className="font-medium">{formData.discount_percentage}%</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Prezzo scontato</p>
                      <p className="font-medium text-primary">€{formData.discounted_price}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Validità</p>
                      <p className="font-medium">
                        {new Date(formData.start_date).toLocaleDateString('it-IT')} - {new Date(formData.end_date).toLocaleDateString('it-IT')}
                      </p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-muted-foreground mb-2">Immagini</p>
                    <div className="grid grid-cols-4 gap-2">
                      {formData.images.map((file, i) => (
                        <div key={i} className="aspect-square border rounded-lg overflow-hidden">
                          <img 
                            src={URL.createObjectURL(file)} 
                            alt={`Immagine ${i+1}`} 
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ))}
                      {formData.images.length === 0 && (
                        <div className="col-span-4 text-sm text-muted-foreground">Nessuna immagine caricata</div>
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Stato dell'offerta
                  </label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        status: value as "draft" | "published" | "expired",
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona lo stato" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Bozza</SelectItem>
                      <SelectItem value="published">Pubblicata</SelectItem>
                      <SelectItem value="expired">Scaduta</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  if (!selectedBusiness) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="text-center py-8">
            <p className="text-muted-foreground">Seleziona un'attività dal menu per creare una nuova offerta</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="outline"
            onClick={() => navigate("/deals")}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Torna alle Offerte
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Crea Nuova Offerta</h1>
            <p className="text-muted-foreground">Attività: {selectedBusiness.name}</p>
          </div>
        </div>

        {/* Stepper */}
        <Stepper />
        
        {/* Step content */}
        <div className="min-h-[600px]">
          {renderStepContent()}
        </div>
        
        {/* Navigation buttons */}
        <div className="flex justify-between items-center mt-8 max-w-4xl mx-auto">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrev}
            disabled={currentStep === 0}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Indietro
          </Button>
          
          {currentStep < steps.length - 1 ? (
            <Button
              type="button"
              onClick={handleNext}
            >
              Avanti
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                Annulla
              </Button>
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Salvataggio..." : "Pubblica"}
              </Button>
            </div>
          )}
        </div>

        {/* Dialog per la generazione di immagini */}
        <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Genera immagine con AI</DialogTitle>
              <DialogDescription>
                Descrivi l'immagine che desideri generare per la tua attività
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">
                  Descrizione immagine
                </label>
                <Input
                  placeholder="Es: Interno di un ristorante moderno con tavoli in legno"
                  value={imagePrompt}
                  onChange={(e) => setImagePrompt(e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Lascia vuoto per generare automaticamente in base al tipo di attività
                </p>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsImageDialogOpen(false)}
                  disabled={isGeneratingImage}
                >
                  Annulla
                </Button>
                <Button
                  type="button"
                  onClick={generateImage}
                  disabled={isGeneratingImage}
                >
                  {isGeneratingImage ? "Generazione..." : generatedImageUrl ? "Rigenera" : "Genera"}
                </Button>
              </div>

              {generatedImageUrl && (
                <div className="space-y-2">
                  <div className="border rounded-lg overflow-hidden">
                    <img
                      src={generatedImageUrl}
                      alt="Immagine generata"
                      className="w-full h-auto object-cover"
                    />
                  </div>
                  <div className="flex justify-end">
                    <Button 
                      type="button" 
                      onClick={saveGeneratedImage}
                    >
                      Usa immagine
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default UpsertDeal;
