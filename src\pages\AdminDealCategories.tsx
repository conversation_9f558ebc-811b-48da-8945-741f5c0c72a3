import { useState, useEffect, useMemo, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Search, Edit, Trash2, Filter, ChevronLeft, ChevronRight } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { DealCategoryModal } from "@/components/categories/DealCategoryModal";
import MainLayout from "@/layouts/MainLayout";

interface DealCategory {
  id: string;
  name: string;
  category_id: string;
  created_at: string;
}

interface DealCategoryWithCategory extends DealCategory {
  category_name?: string;
}

const AdminDealCategories = () => {
  const { toast } = useToast();
  const [dealCategories, setDealCategories] = useState<DealCategoryWithCategory[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedDealCategory, setSelectedDealCategory] = useState<DealCategory | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [categories, setCategories] = useState<{id: string, name: string}[]>([]);

  // Fetch categories for filter dropdown
  const fetchCategories = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('id, name')
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }, []);

  // Filter and paginate data
  const filteredDealCategories = useMemo(() => {
    return dealCategories.filter(dealCategory => {
      const matchesSearch = dealCategory.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (dealCategory.category_name && dealCategory.category_name.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = categoryFilter === "all" || dealCategory.category_name === categoryFilter;

      return matchesSearch && matchesCategory;
    });
  }, [dealCategories, searchTerm, categoryFilter]);

  // Pagination logic
  const totalPages = Math.ceil(filteredDealCategories.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedDealCategories = useMemo(() => {
    return filteredDealCategories.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredDealCategories, startIndex, itemsPerPage]);

  const fetchDealCategories = useCallback(async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("deal_categories")
        .select(`
          *,
          categories!inner(name)
        `)
        .order("name");

      if (error) throw error;

      const dealCategoriesWithCategory = data?.map(item => ({
        ...item,
        category_name: item.categories?.name
      })) || [];

      setDealCategories(dealCategoriesWithCategory);
    } catch (error: any) {
      toast({
        title: "Errore",
        description: "Impossibile caricare le categorie deal",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchDealCategories();
    fetchCategories();
  }, [fetchDealCategories, fetchCategories]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, categoryFilter]);

  // Filter and paginate data
  const filteredDealCategories = useMemo(() => {
    return dealCategories.filter(dealCategory => {
      const matchesSearch = dealCategory.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (dealCategory.category_name && dealCategory.category_name.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = categoryFilter === "all" || dealCategory.category_name === categoryFilter;

      return matchesSearch && matchesCategory;
    });
  }, [dealCategories, searchTerm, categoryFilter]);

  // Pagination logic
  const totalPages = Math.ceil(filteredDealCategories.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedDealCategories = useMemo(() => {
    return filteredDealCategories.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredDealCategories, startIndex, itemsPerPage]);

  const handleEdit = (dealCategory: DealCategory) => {
    setSelectedDealCategory(dealCategory);
    setIsModalOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Sei sicuro di voler eliminare questa categoria deal?")) {
      return;
    }

    try {
      const { error } = await supabase
        .from("deal_categories")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Categoria deal eliminata con successo",
      });

      fetchDealCategories();
    } catch (error: any) {
      toast({
        title: "Errore",
        description: error.message || "Impossibile eliminare la categoria deal",
        variant: "destructive",
      });
    }
  };

  const handleCreateNew = () => {
    setSelectedDealCategory(null);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedDealCategory(null);
  };

  const handleModalSuccess = () => {
    fetchDealCategories();
  };

  const handlePreviousPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };

  const clearFilters = () => {
    setSearchTerm("");
    setCategoryFilter("all");
    setCurrentPage(1);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Gestione Categorie Deal</h1>
        </div>
 
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Gestione Categorie Deal</CardTitle>
                <CardDescription>
                  Gestisci le categorie deal del sistema.
                  {filteredDealCategories.length !== dealCategories.length
                    ? `Mostrando ${filteredDealCategories.length} di ${dealCategories.length} categorie`
                    : `Totale: ${dealCategories.length} categorie`
                  }
                </CardDescription>
              </div>
              <Button onClick={handleCreateNew}>
                <Plus className="w-4 h-4 mr-2" />
                Nuova Categoria Deal
              </Button>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Search and Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Filtri e Ricerca
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="Cerca per nome categoria deal o categoria principale..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div className="w-full sm:w-48">
                    <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="Filtra per categoria" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tutte le categorie</SelectItem>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.name}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <Button variant="outline" onClick={clearFilters}>
                    Pulisci Filtri
                  </Button>
                </div>
              </CardContent>
            </Card>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>Categoria Principale</TableHead>
                    <TableHead>Creata il</TableHead>
                    <TableHead className="text-right">Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedDealCategories.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4">
                        {searchTerm || categoryFilter !== "all" ? "Nessuna categoria deal trovata con i filtri applicati" : "Nessuna categoria deal presente"}
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedDealCategories.map((dealCategory) => (
                      <TableRow key={dealCategory.id}>
                        <TableCell className="font-medium">{dealCategory.name}</TableCell>
                        <TableCell>
                          <Badge variant="secondary">{dealCategory.category_name}</Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(dealCategory.created_at).toLocaleDateString("it-IT")}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(dealCategory)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(dealCategory.id)}
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {filteredDealCategories.length > 0 && (
              <div className="flex items-center justify-between px-2 py-4">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium">Righe per pagina</p>
                  <Select
                    value={`${itemsPerPage}`}
                    onValueChange={(value) => {
                      setItemsPerPage(Number(value));
                      setCurrentPage(1);
                    }}
                  >
                    <SelectTrigger className="h-8 w-[70px]">
                      <SelectValue placeholder={itemsPerPage} />
                    </SelectTrigger>
                    <SelectContent side="top">
                      {[5, 10, 20, 30, 50].map((pageSize) => (
                        <SelectItem key={pageSize} value={`${pageSize}`}>
                          {pageSize}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-6 lg:space-x-8">
                  <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                    Pagina {currentPage} di {totalPages}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      className="h-8 w-8 p-0"
                      onClick={handlePreviousPage}
                      disabled={currentPage === 1}
                    >
                      <span className="sr-only">Vai alla pagina precedente</span>
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      className="h-8 w-8 p-0"
                      onClick={handleNextPage}
                      disabled={currentPage === totalPages}
                    >
                      <span className="sr-only">Vai alla pagina successiva</span>
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="text-sm text-muted-foreground">
                  Mostrando {startIndex + 1}-{Math.min(startIndex + itemsPerPage, filteredDealCategories.length)} di {filteredDealCategories.length} risultati
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <DealCategoryModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          dealCategory={selectedDealCategory}
          onSuccess={handleModalSuccess}
        />
      </div>
    </MainLayout>
  );
};

export default AdminDealCategories;