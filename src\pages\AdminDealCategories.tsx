import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Search, Edit, Trash2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { DealCategoryModal } from "@/components/categories/DealCategoryModal";
import MainLayout from "@/layouts/MainLayout";

interface DealCategory {
  id: string;
  name: string;
  category_id: string;
  created_at: string;
}

interface DealCategoryWithCategory extends DealCategory {
  category_name?: string;
}

const AdminDealCategories = () => {
  const { toast } = useToast();
  const [dealCategories, setDealCategories] = useState<DealCategoryWithCategory[]>([]);
  const [filteredDealCategories, setFilteredDealCategories] = useState<DealCategoryWithCategory[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedDealCategory, setSelectedDealCategory] = useState<DealCategory | null>(null);

  useEffect(() => {
    fetchDealCategories();
  }, []);

  useEffect(() => {
    const filtered = dealCategories.filter(dealCategory =>
      dealCategory.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (dealCategory.category_name && dealCategory.category_name.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    setFilteredDealCategories(filtered);
  }, [dealCategories, searchTerm]);

  const fetchDealCategories = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("deal_categories")
        .select(`
          *,
          categories!inner(name)
        `)
        .order("name");

      if (error) throw error;

      const dealCategoriesWithCategory = data?.map(item => ({
        ...item,
        category_name: item.categories?.name
      })) || [];

      setDealCategories(dealCategoriesWithCategory);
    } catch (error: any) {
      toast({
        title: "Errore",
        description: "Impossibile caricare le categorie deal",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (dealCategory: DealCategory) => {
    setSelectedDealCategory(dealCategory);
    setIsModalOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Sei sicuro di voler eliminare questa categoria deal?")) {
      return;
    }

    try {
      const { error } = await supabase
        .from("deal_categories")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Successo",
        description: "Categoria deal eliminata con successo",
      });

      fetchDealCategories();
    } catch (error: any) {
      toast({
        title: "Errore",
        description: error.message || "Impossibile eliminare la categoria deal",
        variant: "destructive",
      });
    }
  };

  const handleCreateNew = () => {
    setSelectedDealCategory(null);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedDealCategory(null);
  };

  const handleModalSuccess = () => {
    fetchDealCategories();
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Gestione Categorie Deal</h1>
        </div>
 
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Gestione Categorie Deal</CardTitle>
                <CardDescription>
                  Gestisci le categorie deal del sistema. Totale: {dealCategories.length}
                </CardDescription>
              </div>
              <Button onClick={handleCreateNew}>
                <Plus className="w-4 h-4 mr-2" />
                Nuova Categoria Deal
              </Button>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Search className="w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Cerca categorie deal..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>Categoria Principale</TableHead>
                    <TableHead>Creata il</TableHead>
                    <TableHead className="text-right">Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDealCategories.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4">
                        {searchTerm ? "Nessuna categoria deal trovata" : "Nessuna categoria deal presente"}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredDealCategories.map((dealCategory) => (
                      <TableRow key={dealCategory.id}>
                        <TableCell className="font-medium">{dealCategory.name}</TableCell>
                        <TableCell>
                          <Badge variant="secondary">{dealCategory.category_name}</Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(dealCategory.created_at).toLocaleDateString("it-IT")}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(dealCategory)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(dealCategory.id)}
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        <DealCategoryModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          dealCategory={selectedDealCategory}
          onSuccess={handleModalSuccess}
        />
      </div>
    </MainLayout>
  );
};

export default AdminDealCategories;